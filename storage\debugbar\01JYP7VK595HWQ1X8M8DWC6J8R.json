{"__meta": {"id": "01JYP7VK595HWQ1X8M8DWC6J8R", "datetime": "2025-06-26 19:46:55", "utime": **********.018753, "method": "GET", "uri": "/application/quiz/test", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750945613.894994, "end": **********.018777, "duration": 1.1237828731536865, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1750945613.894994, "relative_start": 0, "end": **********.941071, "relative_end": **********.941071, "duration": 1.***************, "duration_str": "1.05s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.941091, "relative_start": 1.****************, "end": **********.018781, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "77.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.963026, "relative_start": 1.****************, "end": **********.968282, "relative_end": **********.968282, "duration": 0.005255937576293945, "duration_str": "5.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.998571, "relative_start": 1.***************, "end": **********.015166, "relative_end": **********.015166, "duration": 0.016595125198364258, "duration_str": "16.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x application.quiz.index", "param_count": null, "params": [], "start": **********.002585, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/index.blade.phpapplication.quiz.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fapplication%2Fquiz%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "application.quiz.index"}, {"name": "1x layouts.public.app", "param_count": null, "params": [], "start": **********.012866, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/public/app.blade.phplayouts.public.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fpublic%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.public.app"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.014213, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET application/quiz/test", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.test.index", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:26-56</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T8oHnVZtTBbohzs4vN5EM3YPZrvQFJ60IDQveNab", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.test.index", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@index", "uri": "GET application/quiz/test", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:26-56</a>", "middleware": "web, web", "duration": "1.12s", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-333956965 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-333956965\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-179762659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-179762659\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2011060475 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6IlVnREY2N2ZTUzhnYnoxY3RwTGxpVHc9PSIsInZhbHVlIjoiLzFaU0ZqRUUzSUw4WU93WVZvT3lmbnc5MVZwWjg1QVI3eS92dUVackNScm4zc0U0cS9IWFhyNzR3V0hCdHhGSitHQmFLZ1V3Z2l5ZGJwUnRiNlRuUTFjWjUzajZzaHRMVEsvaWxCZ0xEUDNjZnVRYytCOWNXYVF2a3RoVjdlUlQiLCJtYWMiOiJiOTliOWI5MzNlMzI4MjNiYTNlNTgyYmYwYTVjMWM5ODBmNzk0NzMxYmVkNWE0N2QxZDhhYzkxNTAwM2IxNTYzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5mRzVhQ0FNc2JQTUlNYTUza09uT2c9PSIsInZhbHVlIjoiclVJdnVmemhaZWlJQzdMWkFvdUhCdHhKczA4Ti91bjVTeHJ1eGd2bU9yZG4wUnhmWmYwRzA3TnU5Ly9MRFdKN2trY3dGdit2S3hFTDhQL1p3ZnZNRzVHOVV6cGF5aTh2ZmY4UWxkYmw2eUJXTy81UHMwOExiK2I0L1lyVmhENGciLCJtYWMiOiJlYjQ2NjNlMmIxOTM0MDY0ZjE0MWViYmY3ODNhYzliMTk3ODA2YjAyNzMzZjUxMTRiY2UyOTU1YTYzMzg3OWU0IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6InpBWVgxbFJ6S0ZvcG9GZTNIQUpVdmc9PSIsInZhbHVlIjoiclJTbHJHNGpEUlVoUEZGb1l5dkhNbTNMQmZ6UE5CdG85eUlTVjNwMER4aEllaFg3U1FjTCs5NXhlaXdHcEFmZWxMdzhabVlJMXFRNnhPOVBBUUhDSWRtdVA2T1JwNmN3SndCRElPOVlzd2VxK1M3VTAxckJVWWpIZDl6TExheVUiLCJtYWMiOiJiNTQ3NjQzYTkzMzE2ZDA1MGQxNDY3NmIwZDQ3ZTRiOGI0ZGQyNjEyM2FjZjRjMDY2N2FmYWUzMjE5OWRkOTNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011060475\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1989100963 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">k0I0Ab90pX0kucqm44kOPCeJGuWvfJ3UTzoNuzs1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T8oHnVZtTBbohzs4vN5EM3YPZrvQFJ60IDQveNab</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lndTIKJiGKygRGgE5HustAtck7cP3AlKUCDbH2pu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989100963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1222299009 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 13:46:55 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222299009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784245906 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T8oHnVZtTBbohzs4vN5EM3YPZrvQFJ60IDQveNab</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://blueorange.test/application/quiz/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784245906\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.test.index", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@index"}, "badge": null}}