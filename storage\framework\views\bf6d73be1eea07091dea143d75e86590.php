<?php $__env->startSection('page_title', __('QUIZ REGISTRATION')); ?>

<?php $__env->startSection('custom_css'); ?>
    <style>
        .quiz-info-card {
            background: #685dd8;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .quiz-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .quiz-info-item:last-child {
            margin-bottom: 0;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <h3 class="mb-3 text-left text-center"><b>Quiz Registration</b> for <?php echo e(config('app.name')); ?></h3>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if(session('info')): ?>
        <div class="alert alert-info">
            <?php echo e(session('info')); ?>

        </div>
    <?php endif; ?>

    <form id="formQuizRegistration" class="mb-3" method="POST" action="<?php echo e(route('application.quiz.test.start')); ?>" autocomplete="off">
        <?php echo csrf_field(); ?>

        <div class="mb-3">
            <div class="quiz-info-card">
                <h4 class="mb-3 text-white text-center">
                    <b>Quiz Information</b>
                </h4>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <div class="quiz-info-item">
                    <span><i class="ti ti-list-numbers me-1"></i>Total Questions:</span>
                    <strong>10</strong>
                </div>
                <div class="quiz-info-item">
                    <span><i class="ti ti-clock me-1"></i>Time Limit:</span>
                    <strong>10 minutes</strong>
                </div>
                <div class="quiz-info-item">
                    <span><i class="ti ti-target me-1"></i>Passing Score:</span>
                    <strong>6 out of 10</strong>
                </div>
                <div class="quiz-info-item">
                    <span><i class="ti ti-shuffle me-1"></i>Question Selection:</span>
                    <strong>Random</strong>
                </div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <div class="text-center">
                    <small><i class="ti ti-alert-triangle me-1"></i><strong>Important:</strong> You can only take one quiz. Once started, you must complete it.</small>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="candidate_name" class="form-label">Full Name <sup class="text-danger">*</sup></label>
            <div class="input-group input-group-merge">
                <span class="input-group-text"><i class="ti ti-user"></i></span>
                <input type="text" id="candidate_name" name="candidate_name" class="form-control <?php $__errorArgs = ['candidate_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old('candidate_name')); ?>" placeholder="Enter your full name" autocomplete="off" required autofocus />
            </div>
            <?php $__errorArgs = ['candidate_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-3">
            <label for="candidate_email" class="form-label">Email Address <sup class="text-danger">*</sup></label>
            <div class="input-group input-group-merge">
                <span class="input-group-text"><i class="ti ti-mail"></i></span>
                <input type="email" id="candidate_email" name="candidate_email" class="form-control <?php $__errorArgs = ['candidate_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old('candidate_email')); ?>" placeholder="Enter your email address" autocomplete="off" required />
            </div>
            <?php $__errorArgs = ['candidate_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <button type="submit" class="btn btn-primary text-uppercase text-bold d-grid w-100">
            <span class="fw-bold">
                <?php echo e(__('Start Quiz')); ?>

                <i class="ti ti-brain ms-1"></i>
            </span>
        </button>
    </form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/application/quiz/index.blade.php ENDPATH**/ ?>