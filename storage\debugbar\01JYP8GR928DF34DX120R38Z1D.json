{"__meta": {"id": "01JYP8GR928DF34DX120R38Z1D", "datetime": "2025-06-26 19:58:28", "utime": **********.388656, "method": "GET", "uri": "/application/quiz/test/show/SIQT20250626QW7N", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750946306.163193, "end": **********.388695, "duration": 2.2255020141601562, "duration_str": "2.23s", "measures": [{"label": "Booting", "start": 1750946306.163193, "relative_start": 0, "end": **********.912319, "relative_end": **********.912319, "duration": 1.****************, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.912345, "relative_start": 1.****************, "end": **********.3887, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.945707, "relative_start": 1.****************, "end": **********.955032, "relative_end": **********.955032, "duration": 0.009325027465820312, "duration_str": "9.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.064939, "relative_start": 1.****************, "end": **********.382376, "relative_end": **********.382376, "duration": 0.*****************, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x application.quiz.show", "param_count": null, "params": [], "start": **********.071748, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.phpapplication.quiz.show", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fapplication%2Fquiz%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "application.quiz.show"}]}, "route": {"uri": "GET application/quiz/test/show/{testid}", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.test.show", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:117-162</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00717, "accumulated_duration_str": "7.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `testid` = 'SIQT20250626QW7N' and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["SIQT20250626QW7N"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.031973, "duration": 0.00587, "duration_str": "5.87ms", "memory": 0, "memory_str": null, "filename": "QuizController.php:124", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=124", "ajax": false, "filename": "QuizController.php", "line": "124"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 81.869}, {"sql": "select `quiz_questions`.*, `quiz_question_quiz_test`.`quiz_test_id` as `pivot_quiz_test_id`, `quiz_question_quiz_test`.`quiz_question_id` as `pivot_quiz_question_id`, `quiz_question_quiz_test`.`selected_option` as `pivot_selected_option`, `quiz_question_quiz_test`.`is_correct` as `pivot_is_correct`, `quiz_question_quiz_test`.`answered_at` as `pivot_answered_at`, `quiz_question_quiz_test`.`created_at` as `pivot_created_at`, `quiz_question_quiz_test`.`updated_at` as `pivot_updated_at` from `quiz_questions` inner join `quiz_question_quiz_test` on `quiz_questions`.`id` = `quiz_question_quiz_test`.`quiz_question_id` where `quiz_question_quiz_test`.`quiz_test_id` in (20) and `quiz_questions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.054965, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "QuizController.php:124", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=124", "ajax": false, "filename": "QuizController.php", "line": "124"}, "connection": "blueorange", "explain": null, "start_percent": 81.869, "width_percent": 18.131}]}, "models": {"data": {"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizQuestion%2FQuizQuestion.php&line=1", "ajax": false, "filename": "QuizQuestion.php", "line": "?"}}, "App\\Models\\Quiz\\QuizTest\\QuizTest": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizTest%2FQuizTest.php&line=1", "ajax": false, "filename": "QuizTest.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://blueorange.test/dashboard\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show", "uri": "GET application/quiz/test/show/{testid}", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:117-162</a>", "middleware": "web, web", "duration": "2.23s", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2136890939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2136890939\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1022656916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1022656916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-740036106 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IkRtOGZSbXA0NkkxZnkyVHhRd3l4dWc9PSIsInZhbHVlIjoiK2U4YXZ1am5PcHlBUWo0dEVEVkJoK1h2M2o0SG1Sd1p1aEkzaTRNM0hlZ2xNY01BMVBreXZlWkdxNmxJbkExSlJmaGpuWFdLUy9QdDNORHVaVXhXc011WFBrcUdueENiK1NGQzNHaDVUZVE0TndDdG9EaEo4R0s2Q2xrSWk1ckgiLCJtYWMiOiIyYzhlODZiMzUyMzRmYjY5MWU4YThhMWNlMjI2NWQ2OTMyNjI4ZDlhNzVhNjBlMzM1ZGU3MGUxNjFkYTcxMTE5IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IktyV1hHR2VmeVMzV3NvSEJJSXlWUXc9PSIsInZhbHVlIjoiaExsY1JRWXk3OHE1R0E0VkNqcWt4L0R0c3FDYnU1cjdCWWdzK1ZYZXNIRmQvcTRHbFJFQ0R1aGhocHpzc0diRWIxdUJrVlRTWkgxdzJySFBlRUpRYVFjb2lFY3owQldyeHpaUHNaeFYvaEZqUFdueTF2Ym9HWUM1R3IxOGxYRisiLCJtYWMiOiI4ZGMwYWE3ZjUzMTc2NDNkMWFhOGU4YmU0ZDY4ZjFiN2I4MGM1YzQ4NzlhNjhjZjQ2NzMwYjEwZTkzYzJlZTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740036106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1162066272 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">es8wEEb7YCROJn0ey39GWkKo4FL9Dbo9Ag6TsxeO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162066272\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1446280973 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 13:58:28 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446280973\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1739884195 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739884195\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show"}, "badge": null}}