{"__meta": {"id": "01JYPA2XN9EZKJ7QJ318GW4WJC", "datetime": "2025-06-26 20:25:52", "utime": **********.300606, "method": "GET", "uri": "/application/quiz/test/show/SIQT20250626QW7N", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[20:25:39] LOG.error: Attempt to read property \"name\" on null {\n    \"view\": {\n        \"view\": \"E:\\\\NIGEL\\\\LaravelProjects\\\\laragon\\\\www\\\\BlueOrange\\\\resources\\\\views\\\\application\\\\quiz\\\\show.blade.php\",\n        \"data\": {\n            \"errors\": \"<pre class=sf-dump id=sf-dump-1662706461 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Support\\\\ViewErrorBag<\\/span> {<a class=sf-dump-ref>#926<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">bags<\\/span>: []\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1662706461\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"test\": \"<pre class=sf-dump id=sf-dump-1573168329 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\Quiz\\\\QuizTest\\\\QuizTest<\\/span> {<a class=sf-dump-ref>#1953<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"10 characters\\\">quiz_tests<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:17<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>20<\\/span>\\n    \\\"<span class=sf-dump-key>testid<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">SIQT20250626QW7N<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>creator_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>candidate_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">Jack Dennis<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>candidate_email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"22 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>total_questions<\\/span>\\\" => <span class=sf-dump-num>5<\\/span>\\n    \\\"<span class=sf-dump-key>total_time<\\/span>\\\" => <span class=sf-dump-num>300<\\/span>\\n    \\\"<span class=sf-dump-key>passing_score<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:48<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>attempted_questions<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>total_score<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Running<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:06<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:48<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:17<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>20<\\/span>\\n    \\\"<span class=sf-dump-key>testid<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">SIQT20250626QW7N<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>creator_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>candidate_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">Jack Dennis<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>candidate_email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"22 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>total_questions<\\/span>\\\" => <span class=sf-dump-num>5<\\/span>\\n    \\\"<span class=sf-dump-key>total_time<\\/span>\\\" => <span class=sf-dump-num>300<\\/span>\\n    \\\"<span class=sf-dump-key>passing_score<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:48<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>attempted_questions<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>total_score<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Running<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:06<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-26 16:03:48<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:4<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>questions<\\/span>\\\" => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"Illuminate\\\\Database\\\\Eloquent\\\\Collection\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">Illuminate\\\\Database\\\\Eloquent<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">Collection<\\/span><\\/span> {<a class=sf-dump-ref>#1946<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:5<\\/span> [ &#8230;5]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:12<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">creator_id<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">candidate_name<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">candidate_email<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">total_questions<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">total_time<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">passing_score<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">started_at<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">ended_at<\\/span>\\\"\\n    <span class=sf-dump-index>8<\\/span> => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">attempted_questions<\\/span>\\\"\\n    <span class=sf-dump-index>9<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">total_score<\\/span>\\\"\\n    <span class=sf-dump-index>10<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">auto_submitted<\\/span>\\\"\\n    <span class=sf-dump-index>11<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1573168329\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\"\n        }\n    },\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.827766, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750947937.074215, "end": **********.300697, "duration": 15.226482152938843, "duration_str": "15.23s", "measures": [{"label": "Booting", "start": 1750947937.074215, "relative_start": 0, "end": **********.869659, "relative_end": **********.869659, "duration": 1.****************, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.869704, "relative_start": 1.****************, "end": **********.300701, "relative_end": 3.814697265625e-06, "duration": 13.***************, "duration_str": "13.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.91981, "relative_start": 1.***************, "end": **********.928998, "relative_end": **********.928998, "duration": 0.009187936782836914, "duration_str": "9.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.047147, "relative_start": 1.****************, "end": **********.30072, "relative_end": 2.288818359375e-05, "duration": 13.***************, "duration_str": "13.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Attempt to read property \"name\" on null (View: E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\application\\quiz\\show.blade.php)", "code": 0, "file": "storage/framework/views/69453aa587b38a0c3288a6ff566b84e6.php", "line": 193, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-413262456 data-indent-pad=\"  \"><span class=sf-dump-note>array:62</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">[object ErrorException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">[object ErrorException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2927 title=\"4 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2926 title=\"4 occurrences\">#926</a><samp data-depth=5 id=sf-dump-413262456-ref2926 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21953 title=\"4 occurrences\">#1953</a><samp data-depth=5 id=sf-dump-413262456-ref21953 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250626QW7N</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Jack Dennis</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>300</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Running</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250626QW7N</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Jack Dennis</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>300</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Running</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1946</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1979</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 01</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:04</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 01</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:04</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1980</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21921 title=\"5 occurrences\">#1921</a><samp data-depth=12 id=sf-dump-413262456-ref21921 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                          \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                          \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                          \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n                          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n                          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n                          <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n                          <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n                          <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n                          <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1977</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 02</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:40</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 02</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:40</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1936</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1976</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1939</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1975</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1940</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1974</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1941</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>72</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2927 title=\"4 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2926 title=\"4 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21953 title=\"4 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2927 title=\"4 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2926 title=\"4 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21953 title=\"4 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>207</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2927 title=\"4 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref2926 title=\"4 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-413262456-ref21953 title=\"4 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>159</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>918</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">app/Http/Controllers/Application/Quiz/QuizController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"47 characters\">App\\Http\\Controllers\\Application\\Quiz\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Controllers\\Application\\Quiz\\QuizController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">vendor/realrashid/sweet-alert/src/ToSweetAlert.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">RealRashid\\SweetAlert\\ToSweetAlert</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413262456\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                            <div class=\"mb-2\">\n", "                                                <i class=\"ti ti-user text-primary\" style=\"font-size: 2rem;\"></i>\n", "                                            </div>\n", "                                            <h4 class=\"mb-1\"><?php echo e($test->candidate_name); ?></h4>\n", "                                            <small class=\"text-muted\">Name</small>\n", "                                        </div>\n", "                                    </div>\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fstorage%2Fframework%2Fviews%2F69453aa587b38a0c3288a6ff566b84e6.php&line=193", "ajax": false, "filename": "69453aa587b38a0c3288a6ff566b84e6.php", "line": "193"}}, {"type": "ErrorException", "message": "Attempt to read property \"name\" on null", "code": 0, "file": "storage/framework/views/69453aa587b38a0c3288a6ff566b84e6.php", "line": 193, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1411267599 data-indent-pad=\"  \"><span class=sf-dump-note>array:65</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>255</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">handleError</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"39 characters\">Attempt to read property &quot;name&quot; on null</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>193</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>193</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Bootstrap\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"39 characters\">Attempt to read property &quot;name&quot; on null</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>193</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2927 title=\"5 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2926 title=\"5 occurrences\">#926</a><samp data-depth=5 id=sf-dump-1411267599-ref2926 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21953 title=\"5 occurrences\">#1953</a><samp data-depth=5 id=sf-dump-1411267599-ref21953 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250626QW7N</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Jack Dennis</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>300</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Running</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:17</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250626QW7N</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Jack Dennis</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>300</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Running</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:48</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1946</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1979</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 01</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:04</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 01</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:04</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1980</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:37</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21921 title=\"5 occurrences\">#1921</a><samp data-depth=12 id=sf-dump-1411267599-ref21921 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                          \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                          \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                          \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n                          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n                          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n                          <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n                          <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n                          <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n                          <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                      </samp>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1977</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 02</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:40</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Demo Question 02</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 1</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 2</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 3</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Opt 4</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 21:46:49</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:05:40</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1936</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>2</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:46:35</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1976</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1939</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>3</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:42</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1975</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1940</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>4</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>1</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 19:04:39</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n                <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1974</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:19</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                    \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                    \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                    \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                    \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                    \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>pivot_quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                    \"<span class=sf-dump-key>pivot_quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                    \"<span class=sf-dump-key>pivot_selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                    \"<span class=sf-dump-key>pivot_is_correct</span>\" => <span class=sf-dump-num>0</span>\n                    \"<span class=sf-dump-key>pivot_answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                    \"<span class=sf-dump-key>pivot_created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                    \"<span class=sf-dump-key>pivot_updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>pivot</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Relations\\Pivot\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent\\Relations</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pivot</span></span> {<a class=sf-dump-ref>#1941</a><samp data-depth=11 class=sf-dump-compact>\n                      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"23 characters\">quiz_question_quiz_test</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=12 class=sf-dump-compact>\n                        \"<span class=sf-dump-key>quiz_test_id</span>\" => <span class=sf-dump-num>20</span>\n                        \"<span class=sf-dump-key>quiz_question_id</span>\" => <span class=sf-dump-num>5</span>\n                        \"<span class=sf-dump-key>selected_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                        \"<span class=sf-dump-key>is_correct</span>\" => <span class=sf-dump-num>0</span>\n                        \"<span class=sf-dump-key>answered_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                        \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 16:03:06</span>\"\n                        \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-26 20:21:48</span>\"\n                      </samp>]\n                      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                      +<span class=sf-dump-public title=\"Public property\">pivotParent</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21921 title=\"5 occurrences\">#1921</a>}\n                      #<span class=sf-dump-protected title=\"Protected property\">foreignKey</span>: \"<span class=sf-dump-str title=\"12 characters\">quiz_test_id</span>\"\n                      #<span class=sf-dump-protected title=\"Protected property\">relatedKey</span>: \"<span class=sf-dump-str title=\"16 characters\">quiz_question_id</span>\"\n                    </samp>}\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2927 title=\"5 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2926 title=\"5 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21953 title=\"5 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>72</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/69453aa587b38a0c3288a6ff566b84e6.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2927 title=\"5 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2926 title=\"5 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21953 title=\"5 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2927 title=\"5 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2926 title=\"5 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21953 title=\"5 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>207</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2927 title=\"5 occurrences\">#927</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref2926 title=\"5 occurrences\">#926</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-1411267599-ref21953 title=\"5 occurrences\">#1953</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>159</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>918</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">app/Http/Controllers/Application/Quiz/QuizController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"47 characters\">App\\Http\\Controllers\\Application\\Quiz\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Controllers\\Application\\Quiz\\QuizController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">vendor/realrashid/sweet-alert/src/ToSweetAlert.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">RealRashid\\SweetAlert\\ToSweetAlert</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411267599\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                            <div class=\"mb-2\">\n", "                                                <i class=\"ti ti-user text-primary\" style=\"font-size: 2rem;\"></i>\n", "                                            </div>\n", "                                            <h4 class=\"mb-1\"><?php echo e($test->candidate_name); ?></h4>\n", "                                            <small class=\"text-muted\">Name</small>\n", "                                        </div>\n", "                                    </div>\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fstorage%2Fframework%2Fviews%2F69453aa587b38a0c3288a6ff566b84e6.php&line=193", "ajax": false, "filename": "69453aa587b38a0c3288a6ff566b84e6.php", "line": "193"}}]}, "views": {"count": 17, "nb_templates": 17, "templates": [{"name": "9x application.quiz.show", "param_count": null, "params": [], "start": **********.054428, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/application/quiz/show.blade.phpapplication.quiz.show", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fapplication%2Fquiz%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 9, "name_original": "application.quiz.show"}, {"name": "8x sweetalert::alert", "param_count": null, "params": [], "start": 1750947950.40424, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 8, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET application/quiz/test/show/{testid}", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.test.show", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:117-162</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00852, "accumulated_duration_str": "8.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `testid` = 'SIQT20250626QW7N' and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["SIQT20250626QW7N"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.011926, "duration": 0.0070599999999999994, "duration_str": "7.06ms", "memory": 0, "memory_str": null, "filename": "QuizController.php:124", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=124", "ajax": false, "filename": "QuizController.php", "line": "124"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 82.864}, {"sql": "select `quiz_questions`.*, `quiz_question_quiz_test`.`quiz_test_id` as `pivot_quiz_test_id`, `quiz_question_quiz_test`.`quiz_question_id` as `pivot_quiz_question_id`, `quiz_question_quiz_test`.`selected_option` as `pivot_selected_option`, `quiz_question_quiz_test`.`is_correct` as `pivot_is_correct`, `quiz_question_quiz_test`.`answered_at` as `pivot_answered_at`, `quiz_question_quiz_test`.`created_at` as `pivot_created_at`, `quiz_question_quiz_test`.`updated_at` as `pivot_updated_at` from `quiz_questions` inner join `quiz_question_quiz_test` on `quiz_questions`.`id` = `quiz_question_quiz_test`.`quiz_question_id` where `quiz_question_quiz_test`.`quiz_test_id` in (20) and `quiz_questions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.037072, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "QuizController.php:124", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Application/Quiz/QuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Application\\Quiz\\QuizController.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=124", "ajax": false, "filename": "QuizController.php", "line": "124"}, "connection": "blueorange", "explain": null, "start_percent": 82.864, "width_percent": 17.136}]}, "models": {"data": {"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizQuestion%2FQuizQuestion.php&line=1", "ajax": false, "filename": "QuizQuestion.php", "line": "?"}}, "App\\Models\\Quiz\\QuizTest\\QuizTest": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizTest%2FQuizTest.php&line=1", "ajax": false, "filename": "QuizTest.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://blueorange.test/dashboard\"\n]"}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show", "uri": "GET application/quiz/test/show/{testid}", "controller": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FApplication%2FQuiz%2FQuizController.php&line=117\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Application/Quiz/QuizController.php:117-162</a>", "middleware": "web, web", "duration": "15.52s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1450495686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450495686\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1992549423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992549423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2063345467 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IlR4VXdFNjdPa1NzRGxudm8vRVp0bUE9PSIsInZhbHVlIjoiSXROZERsUjNlR0JlMEJTVFVCdzZEOUtHWkplVDN4SVBERW1pK2I5aDlsZDhPNlpqNmtFemlZL3RwRGlDbEZxVSs1Y3ordDhkNWNqRm9NaFplN2F3OGpObDF3MUV3bG1SY0NjZ25SZ1Fib3BUQ1NwK3BYakp1OEhERms5ZEJGb2QiLCJtYWMiOiJlZTJmYTA0ZDM1MmUxNjIzMTczNThlMjdkYjYzZTg5MTQ0ODVhYzU1YjE2NGNkNmI4ZDRhOGYyNTEyOTg3YTNhIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IldvSWEvbHd6T2MxVlVJMVpvYTJ3eGc9PSIsInZhbHVlIjoiL3hPUW5NdDYrL0hVTkFWZCtUZFR4YnRMRGpyUkhKRW03ZlJuQ1d5OHo3NUlueDFkdjJFMnBCRGdidTc2WC95SmJiNjI2dEhMcm9vVmlXQ21pYnJzNmJHTTFIU1NaenI5RE43RHRpVjNLSGhBV2JSR2h1YWNRcDB1ZHNqZERlUGsiLCJtYWMiOiI4MDIzY2E2NzkxOWRiNzdlZTNiODExMjg4MWY4MWFhMzE2MDU1ODRhZWRjZGNmZGIzODUzODRlMzJjYzE5Yjg5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063345467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-438781508 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">es8wEEb7YCROJn0ey39GWkKo4FL9Dbo9Ag6TsxeO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438781508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-776455293 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 14:25:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776455293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-290467009 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hYo4VWqjQRVcT08JEOfV48TwvaUB5BgE7ka9CB9x</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"67 characters\">https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290467009\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/application/quiz/test/show/SIQT20250626QW7N", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\Application\\Quiz\\QuizController@show"}, "badge": "500 Internal Server Error"}}